# OAuth Configuration Guide

## Ma<PERSON>ah yang Ditemukan

1. **OAuth consent screen menampilkan domain Supabase** instead of "ScapeGIS"
2. **Redirect URL tidak konsisten** antara development dan production

## Solusi untuk Supabase OAuth Configuration

### 1. Google Cloud Console Configuration

Untuk memperbaiki nama aplikasi yang muncul di OAuth consent screen:

1. **Buka Google Cloud Console**: https://console.cloud.google.com/
2. **Pilih project** yang digunakan untuk OAuth (client_id: ************-...)
3. **Navigasi ke**: APIs & Services > OAuth consent screen
4. **Edit OAuth consent screen**:
   - **Application name**: `ScapeGIS`
   - **Application logo**: Upload logo ScapeGIS
   - **Application home page**: `https://scapegis.com` (atau domain production Anda)
   - **Authorized domains**: 
     - `localhost` (untuk development)
     - `scapegis.com` (untuk production)

### 2. Supabase Dashboard Configuration

1. **Login ke Supabase Dashboard**: https://supabase.com/dashboard
2. **Pilih project**: fgpyqyiazgouorgpkavr
3. **Navigasi ke**: Authentication > Providers > Google
4. **Update konfigurasi**:
   - **Site URL**: `http://localhost:3001` (development) atau `https://scapegis.com` (production)
   - **Redirect URLs**: 
     - `http://localhost:3001/auth/callback`
     - `https://scapegis.com/auth/callback`

### 3. Environment Variables

Pastikan file `.env` memiliki konfigurasi yang benar:

```env
# Frontend URL (harus sesuai dengan Supabase Site URL)
REACT_APP_FRONTEND_URL=http://localhost:3001
PORT=3001

# Supabase Configuration
REACT_APP_SUPABASE_URL=https://fgpyqyiazgouorgpkavr.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Testing OAuth Flow

Setelah konfigurasi di atas:

1. **Restart development server**: `npm start`
2. **Buka**: http://localhost:3001/login
3. **Klik "Continue with Google"**
4. **Verifikasi**: OAuth consent screen menampilkan "ScapeGIS"
5. **Complete authentication**
6. **Verifikasi**: Redirect ke dashboard berhasil

## Troubleshooting

### Jika masih muncul domain Supabase:
- Clear browser cache dan cookies
- Logout dari Google account dan login ulang
- Tunggu 5-10 menit untuk propagasi perubahan Google Cloud Console

### Jika redirect gagal:
- Pastikan port frontend sesuai dengan konfigurasi Supabase
- Check browser console untuk error messages
- Verifikasi URL di Supabase Dashboard > Authentication > URL Configuration
