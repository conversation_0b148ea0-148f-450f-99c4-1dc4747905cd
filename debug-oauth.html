<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Debug - ScapeGIS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 13px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.success:hover { background: #218838; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .card h3 {
            margin-top: 0;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .param-name {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 OAuth Debug Tool</h1>
            <p>Debugging OAuth callback parameters and flow</p>
            <div id="current-url" class="info">
                <strong>Current URL:</strong> <span id="url-display"></span>
            </div>
        </div>
        
        <div id="status-section"></div>
        
        <div class="grid">
            <div class="card">
                <h3>📋 URL Parameters</h3>
                <div id="url-params"></div>
            </div>
            
            <div class="card">
                <h3>💾 localStorage</h3>
                <div id="local-storage"></div>
            </div>
        </div>
        
        <div class="card">
            <h3>🚀 Test Actions</h3>
            <button onclick="testGoogleOAuth()">Test Google OAuth</button>
            <button onclick="simulateCallback()">Simulate Callback</button>
            <button onclick="goToReactApp()" class="success">Go to React App</button>
            <button onclick="clearAll()" class="danger">Clear All Data</button>
        </div>
        
        <div class="card">
            <h3>📊 Expected vs Actual Parameters</h3>
            <div id="param-comparison"></div>
        </div>
        
        <div id="logs-section" class="card">
            <h3>📝 Debug Logs</h3>
            <div id="logs"></div>
        </div>
    </div>

    <script>
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ timestamp, message, type });
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.map(l => 
                `<div style="color: ${getLogColor(l.type)};">[${l.timestamp}] ${l.type.toUpperCase()}: ${l.message}</div>`
            ).join('');
        }

        function getLogColor(type) {
            switch(type) {
                case 'error': return '#dc3545';
                case 'success': return '#28a745';
                case 'warning': return '#ffc107';
                default: return '#6c757d';
            }
        }

        // Parse and display URL parameters
        function analyzeUrl() {
            const url = window.location.href;
            document.getElementById('url-display').textContent = url;
            
            const urlParams = new URLSearchParams(window.location.search);
            const params = {};
            
            // Get all parameters
            for (const [key, value] of urlParams.entries()) {
                params[key] = value;
            }
            
            log(`Analyzing URL: ${url}`, 'info');
            log(`Found ${Object.keys(params).length} parameters`, 'info');
            
            displayUrlParams(params);
            displayLocalStorage();
            compareParameters(params);
            
            // Check if this looks like an OAuth callback
            if (params.oauth_success || params.success || params.code || params.access_token) {
                log('OAuth callback detected!', 'success');
                displayOAuthStatus(params);
            } else {
                log('No OAuth parameters detected', 'warning');
                displayNoOAuthStatus();
            }
        }

        function displayUrlParams(params) {
            const container = document.getElementById('url-params');
            
            if (Object.keys(params).length === 0) {
                container.innerHTML = '<div class="warning">No URL parameters found</div>';
                return;
            }
            
            let html = '<table><thead><tr><th>Parameter</th><th>Value</th></tr></thead><tbody>';
            
            for (const [key, value] of Object.entries(params)) {
                const decodedValue = key.includes('email') || key.includes('name') || key.includes('avatar') 
                    ? decodeURIComponent(value) : value;
                
                html += `<tr>
                    <td><span class="param-name">${key}</span></td>
                    <td>${decodedValue}</td>
                </tr>`;
            }
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function displayLocalStorage() {
            const container = document.getElementById('local-storage');
            const keys = ['access_token', 'refresh_token', 'user_data', 'user_info'];
            
            let html = '<table><thead><tr><th>Key</th><th>Value</th></tr></thead><tbody>';
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                let displayValue = 'null';
                
                if (value) {
                    if (key.includes('token')) {
                        displayValue = value.substring(0, 30) + '...';
                    } else if (key.includes('data') || key.includes('info')) {
                        try {
                            const parsed = JSON.parse(value);
                            displayValue = JSON.stringify(parsed, null, 2);
                        } catch {
                            displayValue = value;
                        }
                    } else {
                        displayValue = value;
                    }
                }
                
                html += `<tr>
                    <td><span class="param-name">${key}</span></td>
                    <td><pre style="margin:0; background:none; padding:0; border:none;">${displayValue}</pre></td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function compareParameters(actualParams) {
            const expectedParams = [
                'oauth_success', 'provider', 'access_token', 'refresh_token',
                'user_id', 'email', 'name', 'avatar_url'
            ];
            
            const legacyParams = ['success', 'token', 'code', 'error'];
            
            let html = '<h4>Expected OAuth Parameters:</h4><table><thead><tr><th>Parameter</th><th>Status</th><th>Value</th></tr></thead><tbody>';
            
            expectedParams.forEach(param => {
                const hasParam = actualParams.hasOwnProperty(param);
                const value = actualParams[param] || 'missing';
                const status = hasParam ? '✅ Present' : '❌ Missing';
                const statusClass = hasParam ? 'success' : 'error';
                
                html += `<tr>
                    <td><span class="param-name">${param}</span></td>
                    <td><span class="${statusClass}">${status}</span></td>
                    <td>${hasParam ? (param.includes('token') ? value.substring(0, 20) + '...' : value) : '-'}</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            
            html += '<h4>Legacy/Alternative Parameters:</h4><table><thead><tr><th>Parameter</th><th>Status</th><th>Value</th></tr></thead><tbody>';
            
            legacyParams.forEach(param => {
                const hasParam = actualParams.hasOwnProperty(param);
                const value = actualParams[param] || 'missing';
                const status = hasParam ? '⚠️ Present (Legacy)' : '➖ Not Present';
                const statusClass = hasParam ? 'warning' : 'info';
                
                html += `<tr>
                    <td><span class="param-name">${param}</span></td>
                    <td><span class="${statusClass}">${status}</span></td>
                    <td>${hasParam ? value : '-'}</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            
            document.getElementById('param-comparison').innerHTML = html;
        }

        function displayOAuthStatus(params) {
            const container = document.getElementById('status-section');
            
            if (params.oauth_success === 'true') {
                container.innerHTML = `
                    <div class="status success">
                        <h3>✅ OAuth Success Detected!</h3>
                        <p>Provider: <strong>${params.provider || 'Unknown'}</strong></p>
                        <p>This should redirect to React app dashboard.</p>
                    </div>
                `;
                log(`OAuth success for provider: ${params.provider}`, 'success');
            } else if (params.success === 'true') {
                container.innerHTML = `
                    <div class="status warning">
                        <h3>⚠️ Legacy OAuth Success Format</h3>
                        <p>Using old parameter format. Frontend expects 'oauth_success' not 'success'.</p>
                    </div>
                `;
                log('Legacy OAuth success format detected', 'warning');
            } else if (params.code) {
                container.innerHTML = `
                    <div class="status info">
                        <h3>ℹ️ OAuth Code Received</h3>
                        <p>Backend should exchange this code for tokens.</p>
                    </div>
                `;
                log('OAuth authorization code received', 'info');
            } else {
                container.innerHTML = `
                    <div class="status error">
                        <h3>❌ OAuth Error or Incomplete</h3>
                        <p>No success indicators found in parameters.</p>
                    </div>
                `;
                log('OAuth error or incomplete callback', 'error');
            }
        }

        function displayNoOAuthStatus() {
            const container = document.getElementById('status-section');
            container.innerHTML = `
                <div class="status info">
                    <h3>ℹ️ No OAuth Callback Detected</h3>
                    <p>This page can be used to test OAuth flow. Click "Test Google OAuth" to start.</p>
                </div>
            `;
        }

        // Test functions
        function testGoogleOAuth() {
            log('Starting Google OAuth test...', 'info');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }

        function simulateCallback() {
            log('Simulating successful OAuth callback...', 'info');
            const params = new URLSearchParams({
                oauth_success: 'true',
                provider: 'google',
                access_token: 'mock_access_token_' + Date.now(),
                refresh_token: 'mock_refresh_token_' + Date.now(),
                user_id: '12345',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://lh3.googleusercontent.com/a/default-user')
            });
            
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        }

        function goToReactApp() {
            log('Redirecting to React app...', 'info');
            window.location.href = 'http://localhost:3001/dashboard';
        }

        function clearAll() {
            log('Clearing all data...', 'info');
            localStorage.clear();
            window.history.replaceState({}, document.title, window.location.pathname);
            location.reload();
        }

        // Initialize
        window.onload = function() {
            log('OAuth Debug Tool initialized', 'info');
            analyzeUrl();
        };
    </script>
</body>
</html>
