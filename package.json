{"name": "scapegiscursor", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@supabase/supabase-js": "^2.50.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "clsx": "^2.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.519.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "zustand": "^5.0.5"}, "scripts": {"start": "react-scripts start", "start:3001": "$env:PORT=3001; react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/leaflet": "^1.9.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}