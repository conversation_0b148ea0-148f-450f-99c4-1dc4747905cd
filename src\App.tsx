import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { LandingPage, Dashboard, ProjectEditor, IntegrationTest, Login, Register, EmailVerification, ForgotPassword, AuthCallback } from './pages';
import OAuthTest from './pages/OAuthTest';
import { ProtectedRoute } from './components/molecules';
import NewProtectedRoute from './components/NewProtectedRoute';
import DevPanel from './components/molecules/DevPanel';
import { AuthProvider } from './hooks/useAuth';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/email-verification" element={<EmailVerification />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected routes - using new auth system */}
          <Route path="/dashboard" element={<Dashboard />} />
          <Route
            path="/project/:projectId"
            element={
              <NewProtectedRoute>
                <ProjectEditor />
              </NewProtectedRoute>
            }
          />

          {/* Development/Test routes */}
          <Route path="/test" element={<IntegrationTest />} />
          <Route path="/oauth-test" element={<OAuthTest />} />
        </Routes>

        {/* Development Panel - only shows in development */}
        <DevPanel />
      </Router>
    </AuthProvider>
  );
}

export default App;
