import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '../types/auth';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  logout: () => void;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (authenticated: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🔍 AuthProvider: Initializing authentication check');
    const token = localStorage.getItem('access_token');
    const userData = localStorage.getItem('user_data');
    
    console.log('🔍 AuthProvider: Token found:', token ? `${token.substring(0, 20)}...` : 'null');
    console.log('🔍 AuthProvider: User data found:', userData ? 'yes' : 'no');
    
    if (token && userData) {
      try {
        const parsedUser: User = JSON.parse(userData);
        console.log('✅ AuthProvider: User authenticated from storage:', parsedUser);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('❌ AuthProvider: Error parsing user data:', error);
        localStorage.removeItem('user_data');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      }
    } else {
      console.log('ℹ️ AuthProvider: No authentication data found');
    }
    setLoading(false);
  }, []);

  const logout = () => {
    console.log('🚪 AuthProvider: Logging out user');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
    setUser(null);
    setIsAuthenticated(false);
  };

  const contextValue = {
    user,
    isAuthenticated,
    loading,
    logout,
    setUser,
    setIsAuthenticated
  };

  console.log('🔍 AuthProvider: Current state:', { 
    isAuthenticated, 
    loading, 
    hasUser: !!user 
  });

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
