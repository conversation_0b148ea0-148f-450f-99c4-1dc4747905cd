import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { AuthContainer } from '../../components/organisms';
import { Typography } from '../../components/atoms';
import { CheckCircle, AlertCircle, Loader } from 'lucide-react';

const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing authentication...');

  const { login, clearError, handleOAuthCallback } = useAuthStore();

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Clear any previous errors
        clearError();

        // Get parameters from URL - using the correct parameter names from backend
        const oauthSuccess = searchParams.get('oauth_success');
        const provider = searchParams.get('provider');
        const message = searchParams.get('message');
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const email = searchParams.get('email');
        const name = searchParams.get('name');
        const avatarUrl = searchParams.get('avatar_url');
        const oauthError = searchParams.get('oauth_error');
        const error = searchParams.get('error');
        const code = searchParams.get('code');

        console.log('🔍 AuthCallback: Processing OAuth callback with parameters:', {
          oauthSuccess,
          provider,
          accessToken: accessToken ? `${accessToken.substring(0, 20)}...` : null,
          userId,
          email,
          name
        });

        // Handle OAuth error
        if (oauthError === 'true' || error) {
          setStatus('error');
          setMessage(`Authentication failed: ${message || error || 'Unknown error'}`);
          console.error('❌ AuthCallback: OAuth error detected');
          return;
        }

        // Handle successful OAuth authentication
        if (oauthSuccess === 'true' && provider && accessToken) {
          setStatus('success');
          setMessage(`${provider} authentication successful! Redirecting to dashboard...`);
          console.log(`✅ AuthCallback: OAuth success for ${provider}`);

          // Store real JWT tokens from backend
          localStorage.setItem('access_token', accessToken);
          if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken);
          }

          // Store user data from OAuth response
          const userData = {
            id: userId || `user_${Date.now()}`,
            email: email ? decodeURIComponent(email) : `user@${provider}.com`,
            name: name ? decodeURIComponent(name) : `${provider} User`,
            provider: provider,
            avatar_url: avatarUrl ? decodeURIComponent(avatarUrl) : undefined
          };
          localStorage.setItem('user_data', JSON.stringify(userData));
          console.log('💾 AuthCallback: Stored authentication data:', userData);

          // Update auth store to mark user as authenticated
          try {
            await handleOAuthCallback(provider, true);
            console.log('✅ AuthCallback: Auth store updated successfully');

            // Redirect to dashboard after auth state is updated
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 500);
          } catch (error) {
            console.error('❌ AuthCallback: Failed to update auth store:', error);
            // Fallback: still redirect but may need to login again
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 1500);
          }
          return;
        }

        // Handle legacy token format (fallback)
        const legacyToken = searchParams.get('token');
        if (legacyToken) {
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');
          localStorage.setItem('access_token', legacyToken);

          setTimeout(() => {
            navigate('/dashboard', { replace: true });
          }, 1500);
          return;
        }

        // Handle OAuth code (needs token exchange)
        if (code) {
          setStatus('error');
          setMessage('OAuth code received but token exchange not implemented');
          console.error('❌ AuthCallback: OAuth code received but no token exchange implemented');
          return;
        }

        // No valid authentication data
        setStatus('error');
        setMessage('No valid authentication data received');
        console.error('❌ AuthCallback: No valid authentication data found');

      } catch (error) {
        console.error('❌ AuthCallback: Unexpected error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred during authentication');
      }
    };

    processCallback();
  }, [searchParams, navigate, clearError]);

  const getIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader className="w-12 h-12 text-blue-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-12 h-12 text-red-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <AuthContainer>
      <div className="text-center">
        {/* Icon */}
        <div className="mx-auto flex items-center justify-center w-16 h-16 mb-6">
          {getIcon()}
        </div>

        {/* Status Message */}
        <Typography variant="h2" className={`mb-3 text-2xl font-semibold ${getStatusColor()}`}>
          {status === 'processing' && 'Authenticating...'}
          {status === 'success' && 'Success!'}
          {status === 'error' && 'Authentication Failed'}
        </Typography>
        
        <Typography variant="body1" color="gray" className="text-sm mb-6 leading-relaxed">
          {message}
        </Typography>

        {/* Actions */}
        {status === 'error' && (
          <div className="space-y-3">
            <button
              onClick={() => navigate('/login')}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Back to Login
            </button>
            <button
              onClick={() => window.location.reload()}
              className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {status === 'processing' && (
          <div className="text-xs text-gray-500 mt-4">
            Please wait while we complete your authentication...
          </div>
        )}
      </div>
    </AuthContainer>
  );
};

export default AuthCallback;
