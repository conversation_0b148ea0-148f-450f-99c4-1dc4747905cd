import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Typography } from '../../components/atoms';
import { AlertCircle } from 'lucide-react';
import { AuthContainer } from '../../components/organisms';

function parseHash(hash: string) {
  return hash
    .substring(1)
    .split("&")
    .reduce((res: Record<string, string>, item) => {
      const [key, value] = item.split("=");
      res[key] = decodeURIComponent(value);
      return res;
    }, {});
}

export default function AuthError() {
  const navigate = useNavigate();

  useEffect(() => {
    const hash = window.location.hash;
    if (hash && hash.includes("access_token")) {
      const tokens = parseHash(hash);
      fetch("/api/v1/auth/callback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(tokens),
      })
        .then((res) => {
          if (!res.ok) throw new Error("Backend error");
          return res.json();
        })
        .then(() => {
          navigate("/dashboard");
        })
        .catch(() => {
          // tetap tampilkan error
        });
    }
  }, [navigate]);

  return (
    <AuthContainer>
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center w-16 h-16 mb-6">
          <AlertCircle className="w-12 h-12 text-red-600" />
        </div>
        <Typography variant="h2" className="mb-3 text-2xl font-semibold text-red-600">
          Login Error
        </Typography>
        <Typography variant="body1" color="gray" className="text-sm mb-6 leading-relaxed">
          Terjadi kesalahan saat login atau menghubungkan akun Google Anda.<br />
          Silakan coba lagi atau hubungi admin jika masalah berlanjut.
        </Typography>
        <button
          onClick={() => navigate('/login')}
          className="w-full max-w-xs mx-auto px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Kembali ke Login
        </button>
      </div>
    </AuthContainer>
  );
} 