import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DashboardTemplate } from '../../components/templates';
import { ProjectsOverview } from '../../components/organisms';
import { useDashboardStore } from '../../stores/dashboardStore';
import { useProjectStore } from '../../stores/projectStore';
import { useAuth } from '../../hooks/useAuth';
import { User } from '../../types/auth';
import LoginButton from '../../components/LoginButton';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [showOAuthSuccess, setShowOAuthSuccess] = useState(false);
  const [showOAuthError, setShowOAuthError] = useState(false);
  const [oauthErrorMessage, setOauthErrorMessage] = useState('');

  // Dashboard store
  const {
    recentProjects,
    isLoadingRecent,
    isCreating,
    loadRecentProjects,
    createProject,
    deleteProject,
    duplicateProject
  } = useDashboardStore();

  // Project store
  const { createNewProject } = useProjectStore();

  // Auth context
  const { user: authUser, isAuthenticated, loading, setUser, setIsAuthenticated } = useAuth();

  console.log('📊 Dashboard: Current auth state:', { authUser, isAuthenticated, loading });

  // Handle OAuth callback parameters
  useEffect(() => {
    const handleOAuthCallback = () => {
      // Check for OAuth success
      if (searchParams.get('oauth_success') === 'true') {
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const email = searchParams.get('email');
        const name = searchParams.get('name');
        const avatarUrl = searchParams.get('avatar_url');
        const provider = searchParams.get('provider');

        console.log('🔍 Dashboard: OAuth callback detected:', {
          provider,
          accessToken: accessToken ? `${accessToken.substring(0, 20)}...` : null,
          userId,
          email,
          name
        });

        if (accessToken && email) {
          // Store tokens
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken || '');

          // Create user object
          const user: User = {
            id: userId || '',
            email: decodeURIComponent(email),
            name: decodeURIComponent(name || ''),
            full_name: decodeURIComponent(name || ''),
            avatar_url: avatarUrl ? decodeURIComponent(avatarUrl) : undefined,
            provider: provider || 'google',
            is_verified: true,
          };

          // Store user data
          localStorage.setItem('user_data', JSON.stringify(user));
          setUser(user);
          setIsAuthenticated(true);

          // Clean URL
          navigate('/dashboard', { replace: true });

          console.log('✅ OAuth login successful!', user);

          // Show success notification
          setShowOAuthSuccess(true);
          setTimeout(() => setShowOAuthSuccess(false), 5000);
        }
      }

      // Check for OAuth error
      if (searchParams.get('oauth_error') === 'true') {
        const provider = searchParams.get('provider');
        const message = searchParams.get('message');
        console.error('❌ OAuth error:', { provider, message });

        setShowOAuthError(true);
        setOauthErrorMessage(`${provider} login failed: ${message || 'Unknown error'}`);

        // Clean URL and redirect to login
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    };

    handleOAuthCallback();
  }, [searchParams, navigate, setUser, setIsAuthenticated]);

  // Load recent projects on mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated && !loading) {
      loadRecentProjects().catch((error) => {
        console.error('Failed to load recent projects:', error);
        // Show user-friendly error message
        alert('Failed to load projects. Please check if backend is running on http://localhost:8001');
      });
    }
  }, [loadRecentProjects, isAuthenticated, loading]);

  const handleProjectClick = async (project: any) => {
    console.log('Opening project:', project);
    try {
      // Navigate to existing project editor
      navigate(`/project/${project.id}`);
    } catch (error) {
      console.error('Failed to open project:', error);
    }
  };

  const handleNewProject = async () => {
    console.log('Creating new project');
    try {
      // Create project via API
      const newProject = await createProject('Untitled Project', 'New WebGIS project');

      // Navigate to new project editor
      navigate(`/project/${newProject.id}`);
    } catch (error) {
      console.error('Failed to create project:', error);
      // Fallback to local creation
      const localProject = createNewProject('Untitled Project');
      navigate(`/project/${localProject.id}`);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      try {
        await deleteProject(projectId);
      } catch (error) {
        console.error('Failed to delete project:', error);
        alert('Failed to delete project. Please try again.');
      }
    }
  };

  const handleDuplicateProject = async (projectId: string) => {
    const projectName = prompt('Enter name for duplicated project:');
    if (projectName) {
      try {
        const duplicated = await duplicateProject(projectId, projectName);
        navigate(`/project/${duplicated.id}`);
      } catch (error) {
        console.error('Failed to duplicate project:', error);
        alert('Failed to duplicate project. Please try again.');
      }
    }
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
    setUser(null);
    setIsAuthenticated(false);
    navigate('/login');
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">🗺️ ScapeGIS</h1>
            <h2 className="text-xl text-gray-600 mb-8">Please login to access your dashboard</h2>
            <LoginButton />
          </div>
        </div>
      </div>
    );
  }

  // Prepare user profile for DashboardTemplate
  const userProfile = authUser ? {
    name: authUser.full_name || authUser.name || 'User',
    email: authUser.email || '<EMAIL>',
    workspace: 'Personal Workspace',
    avatar: authUser.avatar_url || undefined,
  } : {
    name: 'User',
    email: '<EMAIL>',
    workspace: 'Personal Workspace',
    avatar: undefined,
  };

  return (
    <DashboardTemplate
      title="Recents"
      subtitle="Your recently accessed projects and maps"
      user={userProfile}
    >
      {/* OAuth Success Notification */}
      {showOAuthSuccess && (
        <div className="mx-6 mb-6">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md shadow-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <div>
                <strong>Success!</strong> You have been logged in successfully with OAuth.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* OAuth Error Notification */}
      {showOAuthError && (
        <div className="mx-6 mb-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md shadow-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div>
                <strong>Error!</strong> {oauthErrorMessage}
              </div>
            </div>
          </div>
        </div>
      )}

      <ProjectsOverview
        projects={recentProjects}
        isLoading={isLoadingRecent}
        isCreating={isCreating}
        onProjectClick={handleProjectClick}
        onNewProject={handleNewProject}
        onDeleteProject={handleDeleteProject}
        onDuplicateProject={handleDuplicateProject}
      />
    </DashboardTemplate>
  );
};

export default Dashboard;
