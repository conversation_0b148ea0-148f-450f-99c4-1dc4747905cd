import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthContainer } from '../../components/organisms';
import { LoginForm, OAuthSection } from '../../components/molecules';
import { useAuth } from '../../hooks/useAuth';
import LoginButton from '../../components/LoginButton';
import { LoginCredentials } from '../../types';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, loading } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Show loading if checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthContainer>
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🗺️ ScapeGIS</h1>
          <h2 className="text-xl text-gray-600">Sign in to your account</h2>
        </div>

        {/* OAuth Login Buttons */}
        <LoginButton />

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with email</span>
          </div>
        </div>

        {/* Traditional Login Form - keeping existing functionality */}
        <LoginForm
          onSubmit={async (credentials: LoginCredentials) => {
            // Handle traditional login if needed
            console.log('Traditional login:', credentials);
          }}
          loading={false}
          error={null}
        />
      </div>
    </AuthContainer>
  );
};

export default Login;
