export interface User {
  id: string;
  email: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;  // ← Property untuk fix error TypeScript
  name?: string;        // ← Untuk compatibility dengan OAuth response
  is_verified?: boolean;
  provider?: string;
  last_login?: string;
  login_count?: number;
  created_at?: string;
}

export interface OAuthCallbackParams {
  oauth_success: string;
  provider: string;
  access_token: string;
  refresh_token: string;
  user_id: string;
  email: string;
  name: string;
  avatar_url: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  tokens: {
    access_token?: string;
    refresh_token?: string;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: User;
}

export interface OAuthProvider {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  color: string;
}
