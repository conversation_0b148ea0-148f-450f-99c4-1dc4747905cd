<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.danger { background: #dc3545; }
        button.warning { background: #ffc107; color: #212529; }
        
        .step {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        .step h3 { margin-top: 0; color: #007bff; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Authentication Flow</h1>
        <p>Test complete OAuth authentication flow with proper state management</p>
        
        <div id="current-status"></div>
        
        <div class="step">
            <h3>Step 1: Test OAuth Callback Simulation</h3>
            <p>Simulate successful OAuth callback to test if authentication works</p>
            <button onclick="simulateOAuthCallback()">Simulate OAuth Success</button>
            <button onclick="simulateOAuthError()">Simulate OAuth Error</button>
        </div>
        
        <div class="step">
            <h3>Step 2: Test Real OAuth Flow</h3>
            <p>Test actual OAuth with backend</p>
            <button onclick="testRealOAuth()">Start Google OAuth</button>
            <button onclick="checkBackendStatus()" class="warning">Check Backend Status</button>
        </div>
        
        <div class="step">
            <h3>Step 3: Test Dashboard Access</h3>
            <p>Test if authenticated user can access dashboard</p>
            <button onclick="goToDashboard()" class="success">Go to Dashboard</button>
            <button onclick="goToProtectedRoute()" class="success">Test Protected Route</button>
        </div>
        
        <div class="step">
            <h3>Step 4: Debug & Reset</h3>
            <p>Debug authentication state and reset if needed</p>
            <button onclick="checkAuthState()">Check Auth State</button>
            <button onclick="clearAuthData()" class="danger">Clear Auth Data</button>
            <button onclick="showLogs()">Show Debug Logs</button>
        </div>
        
        <div class="grid">
            <div class="container">
                <h4>📊 Current Auth State</h4>
                <div id="auth-state"></div>
            </div>
            
            <div class="container">
                <h4>📋 Debug Logs</h4>
                <div id="debug-logs"></div>
            </div>
        </div>
    </div>

    <script>
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ timestamp, message, type });
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateDebugLogs();
        }
        
        function updateDebugLogs() {
            const logsDiv = document.getElementById('debug-logs');
            const recentLogs = logs.slice(-5); // Show last 5 logs
            logsDiv.innerHTML = recentLogs.map(l => 
                `<div style="font-size: 12px; color: ${getLogColor(l.type)};">[${l.timestamp}] ${l.message}</div>`
            ).join('');
        }
        
        function getLogColor(type) {
            switch(type) {
                case 'error': return '#dc3545';
                case 'success': return '#28a745';
                case 'warning': return '#ffc107';
                default: return '#6c757d';
            }
        }
        
        function updateCurrentStatus() {
            const statusDiv = document.getElementById('current-status');
            const token = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            
            if (token && userData) {
                try {
                    const user = JSON.parse(userData);
                    statusDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ User Authenticated</h4>
                            <p><strong>Name:</strong> ${user.name}</p>
                            <p><strong>Email:</strong> ${user.email}</p>
                            <p><strong>Provider:</strong> ${user.provider}</p>
                        </div>
                    `;
                    log('User is authenticated', 'success');
                } catch (e) {
                    statusDiv.innerHTML = '<div class="warning">⚠️ Token found but user data is invalid</div>';
                    log('Invalid user data format', 'warning');
                }
            } else if (token) {
                statusDiv.innerHTML = '<div class="warning">⚠️ Token found but no user data</div>';
                log('Token found but no user data', 'warning');
            } else {
                statusDiv.innerHTML = '<div class="info">ℹ️ User not authenticated</div>';
                log('User not authenticated', 'info');
            }
        }
        
        function checkAuthState() {
            const authStateDiv = document.getElementById('auth-state');
            const token = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const userData = localStorage.getItem('user_data');
            
            const state = {
                hasToken: !!token,
                hasRefreshToken: !!refreshToken,
                hasUserData: !!userData,
                tokenPreview: token ? token.substring(0, 20) + '...' : null,
                userData: userData ? JSON.parse(userData) : null
            };
            
            authStateDiv.innerHTML = `<pre>${JSON.stringify(state, null, 2)}</pre>`;
            log('Auth state checked', 'info');
        }
        
        function simulateOAuthCallback() {
            log('Simulating OAuth callback...', 'info');
            
            // Create mock OAuth data
            const mockData = {
                oauth_success: 'true',
                provider: 'google',
                access_token: 'ya29.mock_access_token_' + Date.now(),
                refresh_token: 'mock_refresh_token_' + Date.now(),
                user_id: '123456789',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://lh3.googleusercontent.com/a/default-user')
            };
            
            // Redirect to AuthCallback with mock data
            const params = new URLSearchParams(mockData);
            const callbackUrl = `http://localhost:3001/auth/callback?${params.toString()}`;
            
            log('Redirecting to AuthCallback with mock data', 'info');
            window.location.href = callbackUrl;
        }
        
        function simulateOAuthError() {
            log('Simulating OAuth error...', 'warning');
            
            const errorData = {
                oauth_error: 'true',
                provider: 'google',
                message: encodeURIComponent('Test OAuth error')
            };
            
            const params = new URLSearchParams(errorData);
            const callbackUrl = `http://localhost:3001/auth/callback?${params.toString()}`;
            
            window.location.href = callbackUrl;
        }
        
        function testRealOAuth() {
            log('Starting real OAuth flow...', 'info');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }
        
        async function checkBackendStatus() {
            log('Checking backend status...', 'info');
            try {
                const response = await fetch('http://localhost:8001/health');
                if (response.ok) {
                    log('Backend is running', 'success');
                    alert('✅ Backend is running on http://localhost:8001');
                } else {
                    log('Backend responded with error', 'error');
                    alert('⚠️ Backend responded but with error status');
                }
            } catch (error) {
                log('Backend is not accessible', 'error');
                alert('❌ Backend is not running or not accessible on http://localhost:8001');
            }
        }
        
        function goToDashboard() {
            log('Navigating to dashboard...', 'info');
            window.location.href = 'http://localhost:3001/dashboard';
        }
        
        function goToProtectedRoute() {
            log('Testing protected route access...', 'info');
            window.location.href = 'http://localhost:3001/project/test-project';
        }
        
        function clearAuthData() {
            log('Clearing authentication data...', 'warning');
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');
            localStorage.removeItem('user_info');
            
            updateCurrentStatus();
            checkAuthState();
            log('Authentication data cleared', 'success');
        }
        
        function showLogs() {
            const logsDiv = document.getElementById('debug-logs');
            logsDiv.innerHTML = logs.map(l => 
                `<div style="font-size: 11px; color: ${getLogColor(l.type)};">[${l.timestamp}] ${l.type.toUpperCase()}: ${l.message}</div>`
            ).join('');
        }
        
        // Initialize
        window.onload = function() {
            log('Authentication flow test initialized', 'info');
            updateCurrentStatus();
            checkAuthState();
        };
        
        // Update status every 5 seconds
        setInterval(updateCurrentStatus, 5000);
    </script>
</body>
</html>
