<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complete OAuth Flow - ScapeGIS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .step.active {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.active h3 {
            color: #28a745;
        }
        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .step.error h3 {
            color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.danger { background: #dc3545; }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; font-size: 13px; }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Test Complete OAuth Flow - ScapeGIS</h1>
            <p>Testing the complete OAuth flow: Landing → Login → Google OAuth → Dashboard</p>
        </div>
        
        <div id="current-status"></div>
        
        <div class="step" id="step1">
            <h3>Step 1: Landing Page</h3>
            <p>Start from the landing page and navigate to login</p>
            <button onclick="goToLanding()">Go to Landing Page</button>
            <button onclick="checkLanding()">Check Landing</button>
        </div>
        
        <div class="step" id="step2">
            <h3>Step 2: Login Page</h3>
            <p>Access login page and verify OAuth buttons are present</p>
            <button onclick="goToLogin()">Go to Login Page</button>
            <button onclick="checkLogin()">Check Login</button>
        </div>
        
        <div class="step" id="step3">
            <h3>Step 3: Google OAuth</h3>
            <p>Start Google OAuth flow</p>
            <button onclick="startGoogleOAuth()">Start Google OAuth</button>
            <button onclick="simulateOAuthSuccess()">Simulate OAuth Success</button>
        </div>
        
        <div class="step" id="step4">
            <h3>Step 4: Dashboard with Projects</h3>
            <p>Verify dashboard loads with recent projects</p>
            <button onclick="goToDashboard()">Go to Dashboard</button>
            <button onclick="checkDashboard()">Check Dashboard</button>
        </div>
        
        <div class="grid">
            <div class="container">
                <h4>🔧 Debug Tools</h4>
                <button onclick="checkBackend()">Check Backend</button>
                <button onclick="checkAuthState()">Check Auth State</button>
                <button onclick="clearAuthData()" class="danger">Clear Auth Data</button>
                <button onclick="showLogs()">Show Logs</button>
            </div>
            
            <div class="container">
                <h4>📊 Current State</h4>
                <div id="auth-state"></div>
            </div>
        </div>
        
        <div class="container">
            <h4>📋 Test Results</h4>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentStep = 1;
        
        function addTestResult(test, result, details = '') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ timestamp, test, result, details });
            updateTestResults();
            updateStepStatus(test, result);
        }
        
        function updateTestResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.slice(-10).map(r => 
                `<div style="margin-bottom: 10px; padding: 8px; border-radius: 4px; background: ${getResultColor(r.result)};">
                    <strong>[${r.timestamp}] ${r.test}:</strong> ${r.result}
                    ${r.details ? `<br><small>${r.details}</small>` : ''}
                </div>`
            ).join('');
        }
        
        function getResultColor(result) {
            switch(result) {
                case 'PASS': return '#d4edda';
                case 'FAIL': return '#f8d7da';
                case 'RUNNING': return '#fff3cd';
                default: return '#d1ecf1';
            }
        }
        
        function updateStepStatus(test, result) {
            // Update step visual status based on test results
            if (test.includes('Landing')) {
                updateStep('step1', result);
            } else if (test.includes('Login')) {
                updateStep('step2', result);
            } else if (test.includes('OAuth')) {
                updateStep('step3', result);
            } else if (test.includes('Dashboard')) {
                updateStep('step4', result);
            }
        }
        
        function updateStep(stepId, result) {
            const step = document.getElementById(stepId);
            step.className = 'step';
            if (result === 'PASS') {
                step.classList.add('active');
            } else if (result === 'FAIL') {
                step.classList.add('error');
            }
        }
        
        function updateCurrentStatus() {
            const statusDiv = document.getElementById('current-status');
            const token = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            
            if (token && userData) {
                try {
                    const user = JSON.parse(userData);
                    statusDiv.innerHTML = `
                        <div class="status success">
                            <h4>✅ User Authenticated</h4>
                            <p><strong>Name:</strong> ${user.name || user.full_name}</p>
                            <p><strong>Email:</strong> ${user.email}</p>
                            <p><strong>Provider:</strong> ${user.provider}</p>
                        </div>
                    `;
                } catch (e) {
                    statusDiv.innerHTML = '<div class="status warning">⚠️ Token found but user data is invalid</div>';
                }
            } else if (token) {
                statusDiv.innerHTML = '<div class="status warning">⚠️ Token found but no user data</div>';
            } else {
                statusDiv.innerHTML = '<div class="status info">ℹ️ User not authenticated - Ready to test OAuth flow</div>';
            }
        }
        
        function updateAuthState() {
            const stateDiv = document.getElementById('auth-state');
            const token = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            
            const state = {
                hasToken: !!token,
                hasUserData: !!userData,
                currentUrl: window.location.href,
                frontendPort: window.location.port || '3000'
            };
            
            if (userData) {
                try {
                    state.user = JSON.parse(userData);
                } catch (e) {
                    state.userDataError = 'Invalid JSON';
                }
            }
            
            stateDiv.innerHTML = `<pre>${JSON.stringify(state, null, 2)}</pre>`;
        }
        
        // Step 1: Landing Page
        function goToLanding() {
            addTestResult('Landing Page Navigation', 'RUNNING', 'Navigating to landing page...');
            window.location.href = 'http://localhost:3000/';
        }
        
        function checkLanding() {
            addTestResult('Landing Page Check', 'RUNNING', 'Checking if landing page is accessible...');
            fetch('http://localhost:3000/')
                .then(response => {
                    if (response.ok) {
                        addTestResult('Landing Page Check', 'PASS', 'Landing page is accessible');
                    } else {
                        addTestResult('Landing Page Check', 'FAIL', `HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    addTestResult('Landing Page Check', 'FAIL', 'Frontend not running on port 3000');
                });
        }
        
        // Step 2: Login Page
        function goToLogin() {
            addTestResult('Login Page Navigation', 'RUNNING', 'Navigating to login page...');
            window.location.href = 'http://localhost:3000/login';
        }
        
        function checkLogin() {
            addTestResult('Login Page Check', 'RUNNING', 'Checking login page...');
            fetch('http://localhost:3000/login')
                .then(response => {
                    if (response.ok) {
                        addTestResult('Login Page Check', 'PASS', 'Login page is accessible');
                    } else {
                        addTestResult('Login Page Check', 'FAIL', `HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    addTestResult('Login Page Check', 'FAIL', 'Login page not accessible');
                });
        }
        
        // Step 3: OAuth
        function startGoogleOAuth() {
            addTestResult('Google OAuth Start', 'RUNNING', 'Starting real Google OAuth...');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }
        
        function simulateOAuthSuccess() {
            addTestResult('OAuth Simulation', 'RUNNING', 'Simulating OAuth success...');
            
            const params = new URLSearchParams({
                oauth_success: 'true',
                provider: 'google',
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock_token_' + Date.now(),
                refresh_token: 'mock_refresh_' + Date.now(),
                user_id: '123456789',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://lh3.googleusercontent.com/a/default-user')
            });
            
            window.location.href = `http://localhost:3000/dashboard?${params.toString()}`;
        }
        
        // Step 4: Dashboard
        function goToDashboard() {
            addTestResult('Dashboard Navigation', 'RUNNING', 'Navigating to dashboard...');
            window.location.href = 'http://localhost:3000/dashboard';
        }
        
        function checkDashboard() {
            addTestResult('Dashboard Check', 'RUNNING', 'Checking dashboard accessibility...');
            fetch('http://localhost:3000/dashboard')
                .then(response => {
                    if (response.ok) {
                        addTestResult('Dashboard Check', 'PASS', 'Dashboard is accessible');
                    } else {
                        addTestResult('Dashboard Check', 'FAIL', `HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    addTestResult('Dashboard Check', 'FAIL', 'Dashboard not accessible');
                });
        }
        
        // Debug functions
        async function checkBackend() {
            addTestResult('Backend Check', 'RUNNING', 'Checking backend status...');
            
            try {
                const response = await fetch('http://localhost:8001/api/v1/auth/debug/oauth-info');
                if (response.ok) {
                    addTestResult('Backend Check', 'PASS', 'Backend is running and OAuth is configured');
                } else {
                    addTestResult('Backend Check', 'FAIL', `Backend HTTP ${response.status}`);
                }
            } catch (error) {
                addTestResult('Backend Check', 'FAIL', 'Backend not accessible on port 8001');
            }
        }
        
        function checkAuthState() {
            updateAuthState();
            addTestResult('Auth State Check', 'INFO', 'Auth state updated');
        }
        
        function clearAuthData() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');
            updateCurrentStatus();
            updateAuthState();
            addTestResult('Clear Auth Data', 'INFO', 'Authentication data cleared');
        }
        
        function showLogs() {
            const logsDiv = document.getElementById('test-results');
            logsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Initialize
        window.onload = function() {
            addTestResult('Page Load', 'INFO', 'OAuth flow test page initialized');
            updateCurrentStatus();
            updateAuthState();
            
            // Check URL parameters for OAuth callback
            const params = new URLSearchParams(window.location.search);
            if (params.get('oauth_success') === 'true') {
                addTestResult('OAuth Callback Detected', 'PASS', `Provider: ${params.get('provider')}`);
                updateStep('step3', 'PASS');
            }
            
            // Update status every 5 seconds
            setInterval(() => {
                updateCurrentStatus();
                updateAuthState();
            }, 5000);
        };
    </script>
</body>
</html>
