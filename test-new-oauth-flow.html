<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New OAuth Flow - ScapeGIS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; font-size: 13px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.danger { background: #dc3545; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .card h3 { margin-top: 0; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Test New OAuth Flow - ScapeGIS</h1>
            <p>Testing the new OAuth implementation with AuthProvider</p>
        </div>
        
        <div id="status"></div>
        
        <div class="grid">
            <div class="card">
                <h3>🚀 OAuth Tests</h3>
                <button onclick="testGoogleOAuth()">Test Google OAuth</button>
                <button onclick="testGitHubOAuth()">Test GitHub OAuth</button>
                <button onclick="simulateOAuthSuccess()">Simulate Success</button>
                <button onclick="simulateOAuthError()">Simulate Error</button>
            </div>
            
            <div class="card">
                <h3>🔧 Navigation Tests</h3>
                <button onclick="goToLogin()" class="success">Go to Login</button>
                <button onclick="goToDashboard()" class="success">Go to Dashboard</button>
                <button onclick="testProtectedRoute()">Test Protected Route</button>
                <button onclick="checkBackend()">Check Backend</button>
            </div>
        </div>
        
        <div class="card">
            <h3>📊 Current State</h3>
            <div id="current-state"></div>
        </div>
        
        <div class="card">
            <h3>🧪 Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function addTestResult(test, result, details = '') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ timestamp, test, result, details });
            updateTestResults();
        }
        
        function updateTestResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.slice(-5).map(r => 
                `<div style="margin-bottom: 10px; padding: 8px; border-radius: 4px; background: ${r.result === 'PASS' ? '#d4edda' : r.result === 'FAIL' ? '#f8d7da' : '#fff3cd'};">
                    <strong>[${r.timestamp}] ${r.test}:</strong> ${r.result}
                    ${r.details ? `<br><small>${r.details}</small>` : ''}
                </div>`
            ).join('');
        }
        
        function updateCurrentState() {
            const stateDiv = document.getElementById('current-state');
            const token = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            
            let state = {
                hasToken: !!token,
                hasUserData: !!userData,
                currentUrl: window.location.href
            };
            
            if (userData) {
                try {
                    state.user = JSON.parse(userData);
                } catch (e) {
                    state.userDataError = 'Invalid JSON';
                }
            }
            
            stateDiv.innerHTML = `<pre>${JSON.stringify(state, null, 2)}</pre>`;
        }
        
        function checkUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const statusDiv = document.getElementById('status');
            
            if (params.get('oauth_success') === 'true') {
                statusDiv.innerHTML = `
                    <div class="status success">
                        <h4>✅ OAuth Success Detected!</h4>
                        <p>Provider: ${params.get('provider')}</p>
                        <p>This should trigger the new OAuth flow in React app.</p>
                    </div>
                `;
                addTestResult('OAuth Callback', 'PASS', `Provider: ${params.get('provider')}`);
            } else if (params.get('oauth_error') === 'true') {
                statusDiv.innerHTML = `
                    <div class="status error">
                        <h4>❌ OAuth Error Detected!</h4>
                        <p>Provider: ${params.get('provider')}</p>
                        <p>Message: ${params.get('message')}</p>
                    </div>
                `;
                addTestResult('OAuth Callback', 'FAIL', params.get('message'));
            } else {
                statusDiv.innerHTML = `
                    <div class="status info">
                        <h4>ℹ️ Ready for Testing</h4>
                        <p>Use the buttons below to test OAuth flow.</p>
                    </div>
                `;
            }
        }
        
        // Test functions
        function testGoogleOAuth() {
            addTestResult('Google OAuth', 'RUNNING', 'Redirecting to backend...');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }
        
        function testGitHubOAuth() {
            addTestResult('GitHub OAuth', 'RUNNING', 'Redirecting to backend...');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/github';
        }
        
        function simulateOAuthSuccess() {
            addTestResult('Simulate Success', 'RUNNING', 'Creating mock OAuth success...');
            
            const params = new URLSearchParams({
                oauth_success: 'true',
                provider: 'google',
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock_token_' + Date.now(),
                refresh_token: 'mock_refresh_' + Date.now(),
                user_id: '123456789',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://lh3.googleusercontent.com/a/default-user')
            });
            
            // Redirect to React app dashboard with OAuth params
            window.location.href = `http://localhost:3001/dashboard?${params.toString()}`;
        }
        
        function simulateOAuthError() {
            addTestResult('Simulate Error', 'RUNNING', 'Creating mock OAuth error...');
            
            const params = new URLSearchParams({
                oauth_error: 'true',
                provider: 'google',
                message: encodeURIComponent('Test OAuth error simulation')
            });
            
            window.location.href = `http://localhost:3001/dashboard?${params.toString()}`;
        }
        
        function goToLogin() {
            addTestResult('Navigate to Login', 'RUNNING', 'Going to login page...');
            window.location.href = 'http://localhost:3001/login';
        }
        
        function goToDashboard() {
            addTestResult('Navigate to Dashboard', 'RUNNING', 'Going to dashboard...');
            window.location.href = 'http://localhost:3001/dashboard';
        }
        
        function testProtectedRoute() {
            addTestResult('Protected Route Test', 'RUNNING', 'Testing project route...');
            window.location.href = 'http://localhost:3001/project/test-project';
        }
        
        async function checkBackend() {
            addTestResult('Backend Check', 'RUNNING', 'Checking backend status...');
            
            try {
                const response = await fetch('http://localhost:8001/api/v1/auth/debug/oauth-info');
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('Backend Check', 'PASS', 'Backend is running and OAuth is configured');
                } else {
                    addTestResult('Backend Check', 'FAIL', `HTTP ${response.status}`);
                }
            } catch (error) {
                addTestResult('Backend Check', 'FAIL', 'Backend not accessible');
            }
        }
        
        // Initialize
        window.onload = function() {
            addTestResult('Page Load', 'PASS', 'Test page initialized');
            checkUrlParams();
            updateCurrentState();
            
            // Update state every 3 seconds
            setInterval(updateCurrentState, 3000);
        };
    </script>
</body>
</html>
