<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test OAuth Callback</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 OAuth Callback Test</h1>
        <div id="status"></div>
        
        <h3>Test URLs:</h3>
        <button onclick="testCorrectFormat()">Test Correct Format</button>
        <button onclick="testLegacyFormat()">Test Legacy Format</button>
        <button onclick="testRealOAuth()">Test Real OAuth</button>
        <button onclick="goToDashboard()" class="success">Go to Dashboard</button>
        <button onclick="clearData()" class="danger">Clear Data</button>
        
        <div id="results"></div>
    </div>

    <script>
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const result = {};
            for (const [key, value] of params.entries()) {
                result[key] = value;
            }
            return result;
        }

        function displayResults() {
            const params = getUrlParams();
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            console.log('URL Parameters:', params);
            
            if (Object.keys(params).length === 0) {
                statusDiv.innerHTML = '<div class="info">No parameters detected. Use test buttons below.</div>';
                return;
            }
            
            // Check for correct OAuth format
            if (params.oauth_success === 'true') {
                statusDiv.innerHTML = '<div class="success">✅ Correct OAuth format detected!</div>';
                
                // Store data like AuthCallback would
                if (params.access_token) {
                    localStorage.setItem('access_token', params.access_token);
                }
                if (params.refresh_token) {
                    localStorage.setItem('refresh_token', params.refresh_token);
                }
                
                const userData = {
                    id: params.user_id || 'test_user',
                    email: params.email ? decodeURIComponent(params.email) : '<EMAIL>',
                    name: params.name ? decodeURIComponent(params.name) : 'Test User',
                    provider: params.provider || 'google',
                    avatar_url: params.avatar_url ? decodeURIComponent(params.avatar_url) : null
                };
                
                localStorage.setItem('user_data', JSON.stringify(userData));
                
                resultsDiv.innerHTML = `
                    <div class="container">
                        <h4>✅ OAuth Success - Data Stored:</h4>
                        <pre>${JSON.stringify(userData, null, 2)}</pre>
                        <p><strong>Next:</strong> This should redirect to React dashboard</p>
                        <button onclick="redirectToReactDashboard()" class="success">Redirect to React Dashboard</button>
                    </div>
                `;
                
                // Auto redirect after 3 seconds
                setTimeout(() => {
                    redirectToReactDashboard();
                }, 3000);
                
            } else if (params.success === 'true') {
                statusDiv.innerHTML = '<div class="warning">⚠️ Legacy OAuth format detected!</div>';
                resultsDiv.innerHTML = `
                    <div class="container">
                        <h4>⚠️ Legacy Format Issues:</h4>
                        <p>Backend is using old parameter names:</p>
                        <ul>
                            <li>Using 'success' instead of 'oauth_success'</li>
                            <li>Using 'token' instead of 'access_token'</li>
                        </ul>
                        <p><strong>Solution:</strong> Backend needs to use correct parameter names</p>
                    </div>
                `;
            } else if (params.code) {
                statusDiv.innerHTML = '<div class="info">ℹ️ OAuth code received</div>';
                resultsDiv.innerHTML = `
                    <div class="container">
                        <h4>ℹ️ OAuth Authorization Code:</h4>
                        <p>Code: ${params.code}</p>
                        <p><strong>Issue:</strong> Backend should exchange this code for tokens and redirect with tokens</p>
                    </div>
                `;
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Unknown OAuth format</div>';
            }
            
            // Show all parameters
            resultsDiv.innerHTML += `
                <div class="container">
                    <h4>📋 All URL Parameters:</h4>
                    <pre>${JSON.stringify(params, null, 2)}</pre>
                </div>
            `;
        }

        function testCorrectFormat() {
            const params = new URLSearchParams({
                oauth_success: 'true',
                provider: 'google',
                access_token: 'ya29.mock_access_token_' + Date.now(),
                refresh_token: 'mock_refresh_token_' + Date.now(),
                user_id: '123456789',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://lh3.googleusercontent.com/a/default-user')
            });
            
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        }

        function testLegacyFormat() {
            const params = new URLSearchParams({
                success: 'true',
                provider: 'google',
                token: 'legacy_token_' + Date.now(),
                message: 'Authentication successful'
            });
            
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        }

        function testRealOAuth() {
            console.log('Starting real OAuth flow...');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }

        function redirectToReactDashboard() {
            console.log('Redirecting to React dashboard...');
            window.location.href = 'http://localhost:3001/dashboard';
        }

        function goToDashboard() {
            window.location.href = 'http://localhost:3001/dashboard';
        }

        function clearData() {
            localStorage.clear();
            window.history.replaceState({}, document.title, window.location.pathname);
            location.reload();
        }

        // Initialize
        window.onload = function() {
            displayResults();
        };
    </script>
</body>
</html>
