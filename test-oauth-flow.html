<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Flow Test - ScapeGIS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 13px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #218838;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .card h3 {
            margin-top: 0;
            color: #495057;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 OAuth Flow Test - ScapeGIS</h1>
            <p>Test alur autentikasi OAuth dan redirect ke dashboard</p>
        </div>
        
        <div id="status"></div>
        <div id="data"></div>
        
        <div class="grid">
            <div class="card">
                <h3>🚀 Test Actions</h3>
                <button onclick="testGoogleOAuth()">Test Google OAuth</button>
                <button onclick="testGitHubOAuth()">Test GitHub OAuth</button>
                <button onclick="simulateSuccess()">Simulate Success</button>
                <button onclick="redirectToDashboard()" class="success">Go to Dashboard</button>
            </div>
            
            <div class="card">
                <h3>🔧 Debug Actions</h3>
                <button onclick="checkStorage()">Check Storage</button>
                <button onclick="checkAuthState()">Check Auth State</button>
                <button onclick="clearStorage()" class="danger">Clear Storage</button>
                <button onclick="showLogs()">Show Logs</button>
            </div>
        </div>
        
        <div id="localStorage"></div>
        <div id="logs"></div>
    </div>

    <script>
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ timestamp, message, type });
            console.log(`[${timestamp}] ${message}`);
        }

        // Parse URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                oauth_success: params.get('oauth_success'),
                provider: params.get('provider'),
                access_token: params.get('access_token'),
                refresh_token: params.get('refresh_token'),
                user_id: params.get('user_id'),
                email: params.get('email'),
                name: params.get('name'),
                avatar_url: params.get('avatar_url'),
                oauth_error: params.get('oauth_error'),
                message: params.get('message')
            };
        }

        // Display status
        function displayStatus() {
            const params = getUrlParams();
            const statusDiv = document.getElementById('status');
            const dataDiv = document.getElementById('data');
            
            if (params.oauth_success === 'true') {
                log('OAuth Success detected!', 'success');
                statusDiv.innerHTML = '<div class="status success">✅ OAuth Authentication Successful!</div>';
                
                // Store tokens and user data
                if (params.access_token) {
                    localStorage.setItem('access_token', params.access_token);
                    log('Access token stored', 'success');
                }
                if (params.refresh_token) {
                    localStorage.setItem('refresh_token', params.refresh_token);
                    log('Refresh token stored', 'success');
                }
                
                // Store user data
                const userData = {
                    id: params.user_id || `user_${Date.now()}`,
                    email: params.email ? decodeURIComponent(params.email) : '',
                    name: params.name ? decodeURIComponent(params.name) : '',
                    provider: params.provider,
                    avatar_url: params.avatar_url ? decodeURIComponent(params.avatar_url) : null
                };
                
                localStorage.setItem('user_data', JSON.stringify(userData));
                log('User data stored', 'success');
                
                dataDiv.innerHTML = `
                    <div class="status info">
                        <h4>📊 OAuth Data Received:</h4>
                        <pre>${JSON.stringify(params, null, 2)}</pre>
                    </div>
                `;
                
                // Auto redirect after 3 seconds
                let countdown = 3;
                const countdownInterval = setInterval(() => {
                    statusDiv.innerHTML = `<div class="status success">✅ OAuth Success! Redirecting to dashboard in ${countdown}s...</div>`;
                    countdown--;
                    if (countdown < 0) {
                        clearInterval(countdownInterval);
                        redirectToDashboard();
                    }
                }, 1000);
                
            } else if (params.oauth_error === 'true') {
                log('OAuth Error detected!', 'error');
                statusDiv.innerHTML = `<div class="status error">❌ OAuth Failed: ${params.message || 'Unknown error'}</div>`;
            } else {
                log('No OAuth parameters detected', 'info');
                statusDiv.innerHTML = '<div class="status info">ℹ️ No OAuth parameters detected. Use test buttons below.</div>';
            }
        }

        // Test OAuth flows
        function testGoogleOAuth() {
            log('Starting Google OAuth test', 'info');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
        }

        function testGitHubOAuth() {
            log('Starting GitHub OAuth test', 'info');
            window.location.href = 'http://localhost:8001/api/v1/auth/oauth/github';
        }

        function simulateSuccess() {
            log('Simulating OAuth success', 'info');
            const mockParams = new URLSearchParams({
                oauth_success: 'true',
                provider: 'google',
                access_token: `mock_token_${Date.now()}`,
                refresh_token: `mock_refresh_${Date.now()}`,
                user_id: '12345',
                email: encodeURIComponent('<EMAIL>'),
                name: encodeURIComponent('Test User'),
                avatar_url: encodeURIComponent('https://via.placeholder.com/150')
            });
            
            window.location.href = `${window.location.pathname}?${mockParams.toString()}`;
        }

        // Check localStorage
        function checkStorage() {
            const token = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            const refreshToken = localStorage.getItem('refresh_token');

            document.getElementById('localStorage').innerHTML = `
                <div class="container">
                    <h3>📦 localStorage Contents:</h3>
                    <div class="status ${token ? 'success' : 'warning'}">
                        <p><strong>Access Token:</strong> ${token ? token.substring(0, 50) + '...' : 'null'}</p>
                        <p><strong>Refresh Token:</strong> ${refreshToken ? refreshToken.substring(0, 50) + '...' : 'null'}</p>
                        <p><strong>User Data:</strong></p>
                        <pre>${userData || 'null'}</pre>
                    </div>
                </div>
            `;
            
            log('Storage checked', 'info');
        }

        // Check auth state (if React app is running)
        function checkAuthState() {
            log('Checking auth state...', 'info');
            // This would need to communicate with React app
            alert('Open browser console and check React DevTools for auth store state');
        }

        // Clear storage
        function clearStorage() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');
            log('Storage cleared', 'info');
            alert('✅ Storage cleared!');
            checkStorage();
        }

        // Show logs
        function showLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = `
                <div class="container">
                    <h3>📋 Debug Logs:</h3>
                    <pre>${logs.map(l => `[${l.timestamp}] ${l.type.toUpperCase()}: ${l.message}`).join('\n')}</pre>
                </div>
            `;
        }

        // Redirect to dashboard
        function redirectToDashboard() {
            log('Redirecting to dashboard', 'info');
            // Clean URL first
            window.history.replaceState({}, document.title, window.location.pathname);
            
            // Try different dashboard URLs
            const dashboardUrls = [
                'http://localhost:3001/dashboard',
                'http://localhost:3000/dashboard',
                '/dashboard'
            ];
            
            // Try first URL
            window.location.href = dashboardUrls[0];
        }

        // Initialize on page load
        window.onload = function() {
            log('OAuth Flow Test initialized', 'info');
            displayStatus();
            checkStorage();
        };
    </script>
</body>
</html>
